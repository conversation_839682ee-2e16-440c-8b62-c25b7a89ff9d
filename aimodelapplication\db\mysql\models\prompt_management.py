from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json

class PromptManagementModel:
    """提示词管理模型类"""
    
    def __init__(self):
        self.table = "prompt_management"
        self.primary_key = "id"
        self.logger = logging()
        # 创建数据库连接池实例
        self.db_pool = MySQLPool()
        
    async def initialize(self):
        """初始化连接池并确保表存在"""
        await self.db_pool.initialize()
        await self._ensure_table_exists()

    async def _ensure_table_exists(self):
        """确保提示词管理表存在"""
        try:
            # 检查表是否存在
            check_sql = "SHOW TABLES LIKE 'prompt_management'"
            result = await self.db_pool.fetch_one(check_sql)

            if not result:
                # 表不存在，创建表
                create_sql = """
                CREATE TABLE `prompt_management` (
                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                  `knowledge_base_id` varchar(64) NOT NULL COMMENT '知识库ID',
                  `business_introduction` text COMMENT '业务介绍',
                  `term_definitions` json COMMENT '名词定义，JSON格式存储 [{"name":"名词","definition":"定义"}]',
                  `business_prompts` json COMMENT '业务提示，JSON格式存储 [{"content":"提示内容"}]',
                  `extra_evidence` json COMMENT '额外依据，JSON格式存储 [{"name":"依据名称","description":"依据描述"}]',
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `uk_knowledge_base_id` (`knowledge_base_id`),
                  KEY `idx_created_at` (`created_at`),
                  KEY `idx_updated_at` (`updated_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词管理表'
                """
                await self.db_pool.execute(create_sql)
                self.logger.info("提示词管理表创建成功")
            else:
                self.logger.info("提示词管理表已存在")
        except Exception as e:
            self.logger.error(f"确保表存在失败: {str(e)}")
            raise
    
    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
    
    async def find_all(self) -> List[Dict[str, Any]]:
        """查询所有提示词管理记录
        
        Returns:
            提示词管理列表
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE is_deleted = 0"
            results = await self.db_pool.fetch_all(sql)
            # 处理JSON字段
            for result in results:
                self._parse_json_fields(result)
            return results
        except Exception as e:
            self.logger.error(f"查询所有提示词管理记录失败: {str(e)}")
            return []
    
    async def find_by_knowledge_base_id(self, knowledge_base_id: str) -> Optional[Dict[str, Any]]:
        """根据知识库ID查询提示词管理记录
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            提示词管理记录，如果不存在返回None
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE knowledge_base_id = %s AND is_deleted = 0"
            result = await self.db_pool.fetch_one(sql, (knowledge_base_id,))
            if result:
                self._parse_json_fields(result)
            return result
        except Exception as e:
            self.logger.error(f"根据知识库ID查询提示词管理记录失败: {str(e)}")
            return None

    async def find_by_id(self, prompt_id: int) -> Optional[Dict[str, Any]]:
        """根据ID查询提示词管理记录

        Args:
            prompt_id: 提示词管理ID

        Returns:
            提示词管理记录，如果不存在返回None
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE id = %s AND is_deleted = 0"
            result = await self.db_pool.fetch_one(sql, (prompt_id,))
            if result:
                self._parse_json_fields(result)
            return result
        except Exception as e:
            self.logger.error(f"根据ID查询提示词管理记录失败: {str(e)}")
            return None
    
    async def insert(self, data: Dict[str, Any]) -> int:
        """插入提示词管理记录
        
        Args:
            data: 要插入的数据，字段名与值的字典
            
        Returns:
            插入成功返回受影响行数，失败返回0
        """
        try:
            # 处理JSON字段
            processed_data = self._prepare_json_fields(data.copy())
            
            # 提取字段名和对应的值
            fields = list(processed_data.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            values = tuple(processed_data.values())
            
            # 构建SQL语句
            sql = f"INSERT INTO {self.table} ({', '.join(fields)}) VALUES ({placeholders})"
            
            # 执行插入操作
            result = await self.db_pool.execute(sql, values)
            return result
        except Exception as e:
            self.logger.error(f"插入提示词管理记录失败: {str(e)}")
            return 0
    
    async def update_by_knowledge_base_id(self, knowledge_base_id: str, data: Dict[str, Any]) -> int:
        """根据知识库ID更新提示词管理记录
        
        Args:
            knowledge_base_id: 知识库ID
            data: 要更新的数据，字段名与值的字典
            
        Returns:
            更新成功返回受影响行数，失败返回0
        """
        try:
            # 处理JSON字段
            processed_data = self._prepare_json_fields(data.copy())
            
            # 构建更新字段
            update_fields = [f"{k} = %s" for k in processed_data.keys()]
            values = list(processed_data.values())
            values.append(knowledge_base_id)  # 添加WHERE条件的值
            
            # 构建SQL语句
            sql = f"""
                UPDATE {self.table} 
                SET {', '.join(update_fields)}
                WHERE knowledge_base_id = %s AND is_deleted = 0
            """
            
            # 执行更新操作
            result = await self.db_pool.execute(sql, tuple(values))
            return result
        except Exception as e:
            self.logger.error(f"更新提示词管理记录失败: {str(e)}")
            return 0

    async def update(self, prompt_id: int, data: Dict[str, Any]) -> int:
        """更新提示词管理记录
        
        Args:
            prompt_id: 提示词管理ID
            data: 要更新的数据，字段名与值的字典
            
        Returns:
            更新成功返回受影响行数，失败返回0
        """
        try:
            # 处理JSON字段
            processed_data = self._prepare_json_fields(data.copy())
            
            # 构建更新字段
            update_fields = [f"{k} = %s" for k in processed_data.keys()]
            values = list(processed_data.values())
            values.append(prompt_id)  # 添加WHERE条件的值
            
            # 构建SQL语句
            sql = f"""
                UPDATE {self.table} 
                SET {', '.join(update_fields)}
                WHERE {self.primary_key} = %s AND is_deleted = 0
            """
            
            # 执行更新操作
            result = await self.db_pool.execute(sql, tuple(values))
            return result
        except Exception as e:
            self.logger.error(f"更新提示词管理记录失败: {str(e)}")
            return 0
    
    async def delete(self, prompt_id: int) -> int:
        """软删除提示词管理记录
        
        Args:
            prompt_id: 提示词管理ID
            
        Returns:
            删除成功返回受影响行数，失败返回0
        """
        try:
            sql = f"UPDATE {self.table} SET is_deleted = 1 WHERE id = %s"
            result = await self.db_pool.execute(sql, (prompt_id,))
            return result
        except Exception as e:
            self.logger.error(f"删除提示词管理记录失败: {str(e)}")
            return 0

    async def delete_by_knowledge_base_id(self, knowledge_base_id: str) -> int:
        """根据知识库ID软删除提示词管理记录
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            删除成功返回受影响行数，失败返回0
        """
        try:
            sql = f"UPDATE {self.table} SET is_deleted = 1 WHERE knowledge_base_id = %s"
            result = await self.db_pool.execute(sql, (knowledge_base_id,))
            return result
        except Exception as e:
            self.logger.error(f"根据知识库ID删除提示词管理记录失败: {str(e)}")
            return 0

    async def upsert_by_knowledge_base_id(self, knowledge_base_id: str, data: Dict[str, Any]) -> int:
        """根据知识库ID插入或更新提示词管理记录
        
        Args:
            knowledge_base_id: 知识库ID
            data: 要保存的数据，字段名与值的字典
            
        Returns:
            操作成功返回受影响行数，失败返回0
        """
        try:
            # 先查询是否存在
            existing = await self.find_by_knowledge_base_id(knowledge_base_id)
            
            if existing:
                # 存在则更新
                return await self.update_by_knowledge_base_id(knowledge_base_id, data)
            else:
                # 不存在则插入
                data['knowledge_base_id'] = knowledge_base_id
                return await self.insert(data)
        except Exception as e:
            self.logger.error(f"插入或更新提示词管理记录失败: {str(e)}")
            return 0

    def _prepare_json_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """准备JSON字段，将Python对象转换为JSON字符串"""
        json_fields = ['term_definitions', 'business_prompts', 'extra_evidence']
        
        for field in json_fields:
            if field in data and data[field] is not None:
                if isinstance(data[field], (list, dict)):
                    data[field] = json.dumps(data[field], ensure_ascii=False)
        
        return data

    def _parse_json_fields(self, result: Dict[str, Any]) -> None:
        """解析JSON字段，将JSON字符串转换为Python对象"""
        json_fields = ['term_definitions', 'business_prompts', 'extra_evidence']
        
        for field in json_fields:
            if field in result and result[field] is not None:
                if isinstance(result[field], str):
                    try:
                        result[field] = json.loads(result[field])
                    except json.JSONDecodeError:
                        self.logger.warning(f"解析JSON字段 {field} 失败: {result[field]}")
                        result[field] = []
