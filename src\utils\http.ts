import axios from 'axios';
import router from '@/router';

const http = axios.create({
  timeout: 120000, // 设置超时时间为2分钟
  withCredentials: true, // 允许跨域请求携带凭证
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

http.defaults.baseURL = 'http://10.113.53.108:8083/api';


// 请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  response => {
    // 成功响应处理（处理业务级错误码）
    if (!response?.data) {
      return Promise.reject(new Error('无效的响应数据'));
    }

    const { code, data, msg } = response.data;
    

    if (![200, 0, 1].includes(code)) {
      return Promise.reject(new Error(msg || '请求失败'));
    }

    // 如果响应中没有数据，返回完整的响应体
    return data ?? response.data;
  },
  error => {
    // 错误响应处理（处理 HTTP 状态码级错误）
    const status = error.response?.status;

    console.log('error.response', error.response);
    
    // 处理特定的 HTTP 状态码
    switch (status) {
      case 401:
        // 清除登录信息并跳转到登录页
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        router.push('/login');
        return Promise.reject(new Error('登录已过期，请重新登录'));
      case 403:
        return Promise.reject(new Error('没有权限访问该资源'));
      case 404:
        return Promise.reject(new Error('请求的资源不存在'));
      case 500:
        return Promise.reject(new Error('服务器内部错误'));
    }

    // 处理网络错误和超时
    if (!error.response) {
      if (error.code === 'ECONNABORTED') {
        return Promise.reject(new Error('请求超时，请稍后重试'));
      }
      return Promise.reject(new Error('网络连接失败，请检查网络设置'));
    }

    // 其他错误的统一处理
    const msg = error.response?.data?.msg || error.message || '请求失败，请稍后重试';
    return Promise.reject(new Error(msg));
  }
);

export default http;