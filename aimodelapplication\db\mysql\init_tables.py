#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库表初始化脚本
用于创建提示词管理相关的数据库表
"""

import asyncio
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from db.mysql.mysql_pool import MySQLPool
from logger import logging

logger = logging()

# 提示词管理表的创建SQL
CREATE_PROMPT_MANAGEMENT_TABLE = """
CREATE TABLE IF NOT EXISTS `prompt_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_base_id` varchar(64) NOT NULL COMMENT '知识库ID',
  `business_introduction` text COMMENT '业务介绍',
  `term_definitions` json COMMENT '名词定义，JSON格式存储 [{"name":"名词","definition":"定义"}]',
  `business_prompts` json COMMENT '业务提示，JSON格式存储 [{"content":"提示内容"}]',
  `extra_evidence` json COMMENT '额外依据，JSON格式存储 [{"name":"依据名称","description":"依据描述"}]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词管理表';
"""

async def init_database_tables():
    """初始化数据库表"""
    db_pool = MySQLPool()
    
    try:
        # 初始化数据库连接池
        await db_pool.initialize()
        logger.info("数据库连接池初始化成功")
        
        # 创建提示词管理表
        await db_pool.execute(CREATE_PROMPT_MANAGEMENT_TABLE)
        logger.info("提示词管理表创建成功")
        
        # 检查表是否存在
        check_sql = "SHOW TABLES LIKE 'prompt_management'"
        result = await db_pool.fetch_one(check_sql)
        
        if result:
            logger.info("提示词管理表验证成功")
            
            # 检查表结构
            desc_sql = "DESCRIBE prompt_management"
            columns = await db_pool.fetch_all(desc_sql)
            logger.info(f"提示词管理表结构: {len(columns)} 个字段")
            for col in columns:
                logger.info(f"  - {col['Field']}: {col['Type']} {col['Null']} {col['Key']} {col['Default']}")
        else:
            logger.error("提示词管理表创建失败")
            
    except Exception as e:
        logger.error(f"初始化数据库表失败: {str(e)}")
        raise
    finally:
        await db_pool.close()
        logger.info("数据库连接池已关闭")

async def check_table_exists():
    """检查表是否存在"""
    db_pool = MySQLPool()
    
    try:
        await db_pool.initialize()
        
        # 检查表是否存在
        check_sql = "SHOW TABLES LIKE 'prompt_management'"
        result = await db_pool.fetch_one(check_sql)
        
        if result:
            logger.info("提示词管理表已存在")
            return True
        else:
            logger.warning("提示词管理表不存在")
            return False
            
    except Exception as e:
        logger.error(f"检查表是否存在失败: {str(e)}")
        return False
    finally:
        await db_pool.close()

if __name__ == "__main__":
    print("开始初始化数据库表...")
    asyncio.run(init_database_tables())
    print("数据库表初始化完成")
