# 提示词管理功能实现文档

## 概述
本文档描述了提示词管理功能的完整实现，包括数据库设计、数据模型、API接口等。

## 实现的功能

### 1. 数据库设计
- **表名**: `prompt_management`
- **主要字段**:
  - `id`: 主键ID (bigint)
  - `knowledge_base_id`: 知识库ID (varchar(64))，唯一索引
  - `business_introduction`: 业务介绍 (text)
  - `term_definitions`: 名词定义 (json)
  - `business_prompts`: 业务提示 (json)
  - `extra_evidence`: 额外依据 (json)
  - `created_at`, `updated_at`: 时间戳
  - `created_by`, `updated_by`: 创建人和更新人
  - `is_deleted`: 软删除标记

### 2. 数据模型 (`PromptManagementModel`)
**文件位置**: `aimodelapplication/db/mysql/models/prompt_management.py`

**主要方法**:
- `find_all()`: 查询所有记录
- `find_by_knowledge_base_id(knowledge_base_id)`: 根据知识库ID查询
- `find_by_id(prompt_id)`: 根据ID查询
- `insert(data)`: 插入新记录
- `update(prompt_id, data)`: 更新记录
- `update_by_knowledge_base_id(knowledge_base_id, data)`: 根据知识库ID更新
- `delete(prompt_id)`: 软删除记录
- `delete_by_knowledge_base_id(knowledge_base_id)`: 根据知识库ID软删除
- `upsert_by_knowledge_base_id(knowledge_base_id, data)`: 插入或更新

**特性**:
- 自动创建数据库表（如果不存在）
- JSON字段自动序列化和反序列化
- 连接池管理
- 错误处理和日志记录

### 3. API接口
**文件位置**: `aimodelapplication/api/v1/prompt_management/views.py`

**接口列表**:

#### 3.1 加载提示词配置
- **URL**: `GET /ai/v1/prompt_management/load`
- **参数**: `knowledge_base_id` (可选，默认为"default")
- **功能**: 加载指定知识库的提示词配置
- **返回**: 如果存在配置则返回具体数据，否则返回默认空配置

#### 3.2 保存提示词配置
- **URL**: `POST /ai/v1/prompt_management/save`
- **参数**: `knowledge_base_id` (可选，默认为"default")
- **请求体**: 
  ```json
  {
    "businessIntroduction": "业务介绍",
    "termDefinitions": [
      {"name": "名词", "definition": "定义"}
    ],
    "businessPrompts": [
      {"content": "提示内容"}
    ],
    "extraEvidence": [
      {"name": "依据名称", "description": "依据描述"}
    ]
  }
  ```
- **功能**: 保存或更新提示词配置（使用upsert逻辑）

#### 3.3 获取配置列表
- **URL**: `GET /ai/v1/prompt_management/list`
- **功能**: 获取所有提示词配置的列表

#### 3.4 删除配置
- **URL**: `DELETE /ai/v1/prompt_management/delete`
- **参数**: `knowledge_base_id`
- **功能**: 软删除指定知识库的提示词配置

### 4. 数据库初始化
**文件位置**: `aimodelapplication/db/mysql/init_tables.py`

**功能**:
- 创建提示词管理表
- 验证表结构
- 可独立运行进行数据库初始化

### 5. 测试脚本

#### 5.1 数据库操作测试
**文件位置**: `aimodelapplication/test_prompt_db.py`
- 测试数据库模型的各种操作

#### 5.2 API接口测试
**文件位置**: `aimodelapplication/test_api.py`
- 测试API接口的功能

#### 5.3 数据验证脚本
**文件位置**: `aimodelapplication/verify_db_data.py`
- 验证数据库中的数据完整性

## 使用方法

### 1. 前端调用示例

#### 加载配置
```javascript
// 加载默认配置
fetch('/ai/v1/prompt_management/load?knowledge_base_id=default')
  .then(response => response.json())
  .then(data => console.log(data));

// 加载特定知识库配置
fetch('/ai/v1/prompt_management/load?knowledge_base_id=my_kb_001')
  .then(response => response.json())
  .then(data => console.log(data));
```

#### 保存配置
```javascript
const config = {
  businessIntroduction: "当前系统为堡垒机系统",
  termDefinitions: [
    {name: "资产", definition: "本地添加的资产，包括主机数据库等"},
    {name: "账号", definition: "创建资产时同步创建或者绑定资产创建"}
  ],
  businessPrompts: [
    {content: "授权是对用户进行（资产，资产账号等）授权"}
  ],
  extraEvidence: []
};

fetch('/ai/v1/prompt_management/save?knowledge_base_id=my_kb_001', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(config)
})
.then(response => response.json())
.then(data => console.log(data));
```

### 2. 后端使用示例

```python
from db.mysql.models.prompt_management import PromptManagementModel

async def example_usage():
    model = PromptManagementModel()
    await model.initialize()
    
    # 保存配置
    data = {
        'business_introduction': '业务介绍',
        'term_definitions': [{'name': '名词', 'definition': '定义'}],
        'business_prompts': [{'content': '提示内容'}],
        'extra_evidence': []
    }
    await model.upsert_by_knowledge_base_id('kb_001', data)
    
    # 加载配置
    config = await model.find_by_knowledge_base_id('kb_001')
    
    await model.close()
```

## 测试结果

### API测试结果
- ✅ 加载接口正常工作
- ✅ 保存接口正常工作
- ✅ 数据能够正确保存到数据库
- ✅ 数据能够正确从数据库加载

### 数据库验证结果
- ✅ 数据库表创建成功
- ✅ 数据正确存储（包括JSON字段）
- ✅ 查询功能正常
- ✅ 支持多个知识库ID的数据隔离

## 注意事项

1. **数据库连接**: 每次API调用都会创建新的数据库连接，使用完毕后会自动关闭
2. **JSON字段**: 复杂的数据结构（如数组、对象）会自动转换为JSON格式存储
3. **软删除**: 删除操作使用软删除，数据不会真正从数据库中移除
4. **唯一约束**: 每个知识库ID只能有一条配置记录
5. **错误处理**: 所有操作都有完善的错误处理和日志记录

## 部署说明

1. 确保数据库连接配置正确
2. 运行数据库初始化脚本（可选，模型会自动创建表）
3. 重启应用服务器
4. 测试API接口功能

## 总结

提示词管理功能已经完全实现并测试通过，包括：
- 完整的数据库设计和模型
- 功能完善的API接口
- 自动化的数据库表创建
- 完善的错误处理和日志记录
- 全面的测试验证

现在前端可以正常调用这些接口来保存和加载提示词配置，数据会持久化存储在数据库中。
