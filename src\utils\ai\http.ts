import axios from 'axios';
import { message } from 'ant-design-vue';

const http = axios.create({
  baseURL: 'http://10.113.53.108:8081/ai/v1',
  timeout: 60000,
  maxRedirects: 0,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // 添加跨域请求头
    config.headers['X-Requested-With'] = 'XMLHttpRequest';
    console.log('Request Config:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data
    });
    return config;
  },
  error => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  response => {
    // 处理响应数据
    console.log('Response Data:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    });
    
    if (response.data) {
      // 确保响应数据被正确解析
      try {
        if (typeof response.data === 'string') {
          response.data = JSON.parse(response.data);
        }
      } catch (e) {
        console.warn('Response data parsing failed:', e);
      }
    }
    return response;
  },
  error => {
    // 新增：超时错误友好提示
    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
      message.error('请求超时，请稍后重试');
    }
    if (error.response) {
      // 处理响应错误
      console.error('Response Error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data
      });
      
      // 尝试解析错误响应数据
      try {
        if (error.response.data && typeof error.response.data === 'string') {
          error.response.data = JSON.parse(error.response.data);
        }
      } catch (e) {
        console.warn('Error response data parsing failed:', e);
      }
    } else if (error.request) {
      // 处理请求错误
      console.error('Request Error:', error.request);
    } else {
      // 处理其他错误
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default http;