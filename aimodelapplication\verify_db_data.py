#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证数据库中的提示词管理数据
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from db.mysql.models.prompt_management import PromptManagementModel

async def verify_database_data():
    """验证数据库中的数据"""
    model = PromptManagementModel()
    
    try:
        # 初始化
        await model.initialize()
        print("数据库连接初始化成功")
        
        # 查询所有数据
        all_data = await model.find_all()
        print(f"\n数据库中共有 {len(all_data)} 条提示词管理记录:")
        
        for i, item in enumerate(all_data, 1):
            print(f"\n记录 {i}:")
            print(f"  ID: {item.get('id')}")
            print(f"  知识库ID: {item.get('knowledge_base_id')}")
            print(f"  业务介绍: {item.get('business_introduction')}")
            print(f"  名词定义数量: {len(item.get('term_definitions', []))}")
            print(f"  业务提示数量: {len(item.get('business_prompts', []))}")
            print(f"  额外依据数量: {len(item.get('extra_evidence', []))}")
            print(f"  创建时间: {item.get('created_at')}")
            print(f"  更新时间: {item.get('updated_at')}")
            print(f"  创建人: {item.get('created_by')}")
            print(f"  更新人: {item.get('updated_by')}")
            
            # 显示详细内容
            if item.get('term_definitions'):
                print("  名词定义详情:")
                for j, term in enumerate(item.get('term_definitions', []), 1):
                    print(f"    {j}. {term.get('name', '')}: {term.get('definition', '')}")
            
            if item.get('business_prompts'):
                print("  业务提示详情:")
                for j, prompt in enumerate(item.get('business_prompts', []), 1):
                    print(f"    {j}. {prompt.get('content', '')}")
            
            if item.get('extra_evidence'):
                print("  额外依据详情:")
                for j, evidence in enumerate(item.get('extra_evidence', []), 1):
                    print(f"    {j}. {evidence.get('name', '')}: {evidence.get('description', '')}")
        
        # 测试特定知识库ID的查询
        print(f"\n\n测试查询特定知识库ID的数据:")
        test_ids = ["default", "test_api_001", "test_kb_001"]
        
        for kb_id in test_ids:
            data = await model.find_by_knowledge_base_id(kb_id)
            if data:
                print(f"  知识库ID '{kb_id}' 存在数据")
                print(f"    业务介绍: {data.get('business_introduction', '')[:50]}...")
            else:
                print(f"  知识库ID '{kb_id}' 没有数据")
                
    except Exception as e:
        print(f"验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await model.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    print("开始验证数据库中的提示词管理数据...")
    asyncio.run(verify_database_data())
    print("验证完成")
