import axios from "axios"
import { reqReject, reqResolve, resReject, resResolve } from "./interceptors"

export function createAxios(options = {}) {
  const defaultOptions = {
    baseURL: "http://10.113.53.25:9004/api",
    timeout: 300000,
  }

  const instance = axios.create({
    ...defaultOptions,
    ...options,
  })

  //1. 请求拦截器
  instance.interceptors.request.use(reqResolve, reqReject)
  //2. 响应拦截器
  instance.interceptors.response.use(resResolve, resReject)

  return instance
}

export const defAxios = createAxios({ baseURL: "/itr/v1" })
