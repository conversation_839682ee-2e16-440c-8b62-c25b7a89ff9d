# -*- coding: utf-8 -*-

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
import requests
from template.response_temp import BaseResponse
from logger import connection_logging
from db.mysql.models.prompt_management import PromptManagementModel

router = APIRouter(prefix="/prompt_management", tags=["提示词管理"])
logger = connection_logging()

# 请求模型
class TermDefinition(BaseModel):
    name: str
    definition: str

class BusinessPrompt(BaseModel):
    content: str

class ExtraEvidence(BaseModel):
    name: str
    description: str

class PromptManagementRequest(BaseModel):
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    extraEvidence: List[ExtraEvidence]

class GeneratePrerequisiteRequest(BaseModel):
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    extraEvidence: List[ExtraEvidence]
    apiDocument: str  # 接口文档内容

# 阿里云百炼API配置
DASHSCOPE_API_KEY = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

class DashScopeService:
    """阿里云百炼API服务"""
    
    @staticmethod
    def call_llm(prompt: str, model: str = "qwen-max-latest") -> str:
        """调用阿里云百炼大语言模型"""
        headers = {
            "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.1,        # 温度参数，控制输出的随机性
                "repetition_penalty": 1.0  # 重复惩罚参数
            }
        }
        
        try:
            response = requests.post(DASHSCOPE_API_URL, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            # 处理不同的API响应格式
            if result.get("output"):
                output = result["output"]

                # 新格式：直接返回text字段
                if "text" in output:
                    return output["text"]

                # 旧格式：choices数组格式
                elif "choices" in output and output["choices"]:
                    return output["choices"][0]["message"]["content"]

                else:
                    raise Exception(f"API返回格式异常: {result}")
            else:
                raise Exception(f"API返回格式异常: {result}")

        except requests.exceptions.RequestException as e:
            logger.error(f"调用阿里云百炼API失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"调用大语言模型失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理API响应失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理模型响应失败: {str(e)}")

@router.post("/save", summary="保存提示词配置")
async def save_prompt_config(request: PromptManagementRequest, knowledge_base_id: str = "default"):
    """保存提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始保存提示词配置，knowledge_base_id: {knowledge_base_id}")
        await prompt_model.initialize()
        logger.info("数据库连接初始化成功")

        # 准备数据
        data = {
            'business_introduction': request.businessIntroduction,
            'term_definitions': [term.dict() for term in request.termDefinitions],
            'business_prompts': [prompt.dict() for prompt in request.businessPrompts],
            'extra_evidence': [evidence.dict() for evidence in request.extraEvidence],
            'updated_by': 'system'  # 可以从请求头或认证信息中获取
        }

        logger.info(f"保存提示词配置: {data}")

        # 使用upsert方法，如果存在则更新，不存在则插入
        logger.info(f"开始执行数据库upsert操作，knowledge_base_id: {knowledge_base_id}")
        result = await prompt_model.upsert_by_knowledge_base_id(knowledge_base_id, data)
        logger.info(f"数据库upsert操作结果: {result}")

        if result > 0:
            logger.info(f"提示词配置保存成功，knowledge_base_id: {knowledge_base_id}")
            return BaseResponse(
                code=200,
                message="提示词配置保存成功",
                data={"saved": True, "knowledge_base_id": knowledge_base_id}
            )
        else:
            logger.error(f"数据库操作返回结果为0，保存失败，knowledge_base_id: {knowledge_base_id}")
            raise HTTPException(status_code=500, detail="保存失败，数据库操作未成功")

    except Exception as e:
        logger.error(f"保存提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")
    finally:
        await prompt_model.close()
        logger.info("数据库连接已关闭")

@router.get("/load", summary="加载提示词配置")
async def load_prompt_config(knowledge_base_id: str = "default"):
    """加载提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始加载提示词配置，knowledge_base_id: {knowledge_base_id}")
        await prompt_model.initialize()
        logger.info("数据库连接初始化成功")

        # 从数据库加载配置
        logger.info(f"开始查询数据库，knowledge_base_id: {knowledge_base_id}")
        config = await prompt_model.find_by_knowledge_base_id(knowledge_base_id)
        logger.info(f"数据库查询结果: {config}")

        if config:
            # 转换数据格式以匹配前端期望的格式
            response_data = {
                "businessIntroduction": config.get('business_introduction', ''),
                "termDefinitions": config.get('term_definitions', []),
                "businessPrompts": config.get('business_prompts', []),
                "extraEvidence": config.get('extra_evidence', [])
            }

            logger.info(f"加载提示词配置成功: {knowledge_base_id}, 数据: {response_data}")
        else:
            # 如果没有找到配置，返回默认的空配置
            response_data = {
                "businessIntroduction": "",
                "termDefinitions": [
                    {"name": "", "definition": ""}
                ],
                "businessPrompts": [
                    {"content": ""}
                ],
                "extraEvidence": [
                    {"name": "", "description": ""}
                ]
            }
            logger.info(f"未找到提示词配置，返回默认配置: {knowledge_base_id}")

        return BaseResponse(
            code=200,
            message="加载提示词配置成功",
            data=response_data
        )
    except Exception as e:
        logger.error(f"加载提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"加载失败: {str(e)}")
    finally:
        await prompt_model.close()
        logger.info("数据库连接已关闭")

@router.get("/list", summary="获取所有提示词配置列表")
async def list_prompt_configs():
    """获取所有提示词管理配置列表"""
    prompt_model = PromptManagementModel()
    try:
        await prompt_model.initialize()

        # 获取所有配置
        configs = await prompt_model.find_all()

        # 转换数据格式
        response_data = []
        for config in configs:
            response_data.append({
                "id": config.get('id'),
                "knowledge_base_id": config.get('knowledge_base_id'),
                "businessIntroduction": config.get('business_introduction', ''),
                "termDefinitions": config.get('term_definitions', []),
                "businessPrompts": config.get('business_prompts', []),
                "extraEvidence": config.get('extra_evidence', []),
                "created_at": config.get('created_at'),
                "updated_at": config.get('updated_at'),
                "created_by": config.get('created_by'),
                "updated_by": config.get('updated_by')
            })

        return BaseResponse(
            code=200,
            message="获取提示词配置列表成功",
            data=response_data
        )
    except Exception as e:
        logger.error(f"获取提示词配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")
    finally:
        await prompt_model.close()

@router.delete("/delete", summary="删除提示词配置")
async def delete_prompt_config(knowledge_base_id: str):
    """删除提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        await prompt_model.initialize()

        # 软删除配置
        result = await prompt_model.delete_by_knowledge_base_id(knowledge_base_id)

        if result > 0:
            return BaseResponse(
                code=200,
                message="删除提示词配置成功",
                data={"deleted": True, "knowledge_base_id": knowledge_base_id}
            )
        else:
            raise HTTPException(status_code=404, detail="未找到要删除的配置")

    except Exception as e:
        logger.error(f"删除提示词配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")
    finally:
        await prompt_model.close()

@router.post("/generate_prerequisite", summary="生成前置操作")
async def generate_prerequisite(request: GeneratePrerequisiteRequest):
    """根据提示词配置和接口文档生成前置操作"""
    try:
        # 构建完整的提示词
        prompt = build_complete_prompt(request)
        
        # 调用大语言模型
        result = DashScopeService.call_llm(prompt)
        
        logger.info(f"生成前置操作成功: {result}")
        
        return BaseResponse(
            code=200,
            message="生成前置操作成功",
            data={"prerequisite": result.strip()}
        )
    except Exception as e:
        logger.error(f"生成前置操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

def build_complete_prompt(request: GeneratePrerequisiteRequest) -> str:
    """构建完整的提示词"""

    # 构建名词定义部分
    term_definitions_text = ""
    for term in request.termDefinitions:
        if term.name and term.definition:
            term_definitions_text += f"## {term.name}：{term.definition}\n"

    # 构建额外提示部分
    business_prompts_text = ""
    for prompt in request.businessPrompts:
        if prompt.content:
            business_prompts_text += f"## {prompt.content}\n"

    # 构建额外依据部分
    extra_evidence_text = ""
    for evidence in request.extraEvidence:
        if evidence.name and evidence.description:
            extra_evidence_text += f"## {evidence.name}：{evidence.description}\n"
    
    # 完整提示词模板
    complete_prompt = f"""# 任务描述
根据接口传参判断，在执行接口前需要进行的前置操作

你是一个接口测试专家，擅长根据接口文档判断接口执行顺序。

# 业务介绍
{request.businessIntroduction}

# 名词定义
{term_definitions_text}

# 额外提示
{business_prompts_text}

# 必须满足的判断依据
## 业务场景：参考业务介绍判断出执行当前接口时必须要进行的操作，例如：更新授权规则前必须要条件为创建授权规则
## 接口类型：接口类型为创建类型接口
## 非枚举值：字段类型为非枚举值类型
## 路径嵌入：在url中明文声明的变量所必须依赖的前置操作，例如：/api/{{projectId}} 必须依赖创建项目

# 补充依据
## 字段映射：$ref引用的实体对象标识字段（如assetId→资产ID）
## 接口类型：接口类型为创建类型接口
{extra_evidence_text}

# 任务要求(必须满足) 
- 只考虑一级参数，不考虑嵌套参数。例如：只考虑类似account的一级参数,排除account.sshKeyI等二级参数
- 每个前置接口均需要文档中的参数作为依据
- 不要放过任何一个参数，保证每个参数的前置操作均需要识别
- 额外提示一定要满足

# 接口文档
{request.apiDocument}

# 输出要求
## 仅输出前置接口以及举证参数
## 不要输出原因和描述，只需要给出结果

## 参考输出结果如下：
创建项目（projectId）、创建用户（userName）、创建资产(assetsId)
"""
    
    return complete_prompt
